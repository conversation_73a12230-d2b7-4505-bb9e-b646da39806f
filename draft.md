1、手动精修的图片，更新到了换背景，但没有更新到改尺寸里面
2、manual-refine-canvas生成的是人像为白色、其他为黑色的画布图片，
实际需要的是全部显示 currentImg.file，根据 mattingImage 确定人像部分，
在人像处加一个透明蒙层（颜色可以为#F60000 半透明），
画笔保留时根据画笔处增加这个通明蒙层，画笔擦除时移除画笔处的透明蒙层，
将蒙层区域的图，更新到mattingImage里面

5、window.addEventListener('mattingImageUpdated' 需要补充解绑

没有实现我的需求
1、需要的是manual-refine-canvas移除黑白画图模式
2、首先使用 currentImg.file 填满画布（类似之前的黑白总和），currentImg.file是mattingImage未抠图之前的图片
3、其次根据 mattingImage 确定人像部分，在人像处加一个透明蒙层（颜色可以为#F60000 半透明，这个透明蒙层就是之前的白色部分）
4、关于画笔，画笔保留 增加这个透明蒙层（颜色可以为#F60000 半透明），画笔擦除 擦除透明蒙层部分

所以上面的4点需求，都没有实现，重新分析下
1、需要的是manual-refine-canvas移除黑白画图模式
2、首先使用 currentImg.file 填满画布（类似之前的黑白总和），currentImg.file是mattingImage未抠图之前的图片
3、其次根据 mattingImage 确定人像部分，在人像处加一个透明蒙层（颜色可以为#F60000 半透明，这个透明蒙层就是之前的白色部分）
4、关于画笔，画笔保留 增加这个透明蒙层（颜色可以为#F60000 半透明），画笔擦除 擦除透明蒙层部分
-- 这个版本后生成的图ok

效果实现了，但是问题比较多
1、首次进来，颜色可以为#F60000 30%透明蒙版 没有包含完整的人像
2、每次画笔拖动时，都会重新生成透明蒙版，应该做增量
3、画笔涂画的过程中也应该是半透明的，现在是非透明的（已经透明，重复绘画导致的）
4、画笔速度快时，生成的区块是非连续的
5、撤销、恢复、重置 失效了
6、又发生了手动精修图片没有同步到改尺寸


1、首次进来，颜色可以为#F60000 半透明蒙版 没有包含完整的人像
2、每次画笔拖动时，都会重新生成透明蒙版，应该做增量
3、画笔涂画的过程中也应该是半透明的，现在是非透明的
4、画笔速度快时，生成的区块是非连续的
5、撤销、恢复、重置 失效了
6、又发生了手动精修图片没有同步到改尺寸

-- point18
1、已解决
2、3 解决部分，问题：① 画笔所画区域发生了重复渲染，堆叠导致看起来像非透明，实际上只需要绘制一次，看起来就很透明了（快速拖动时绘制的就透明） ② 快速拖动时绘制的区域正常是没有渲染到mattingImage里面的
5、撤销、恢复、重置 存在丢失的情况，没能记录完整
6、画笔所绘区域生成的图又变成了黑色，上一版是好的(point17)

1、人像处为什么进行了描边？
2、画笔绘画时，同一区域只需要绘画一次，再次过来时，就不需要再绘画了，现在是不断绘画直到颜色越来越深(注意不要又改成了黑色或白色)
3、切换原图、效果图时 导致撤销、恢复、重置 记录丢失
4、将#F60000 透明度0.5改成0.3



point19 基本功能能用，有黑边，保留画笔没下一版好(point20)



1、手动精修1，撤销等是好的；精修还是黑白
2、手动精修2，人像透明绘制是失败的；撤销等也是坏的

// 遍历每个像素，在保留区域（白色区域）创建红色半透明蒙层，搜索：for (let i = 0; i < data.length; i += 4)
for (let i = 0; i < data.length; i += 4) {
  // 检查是否为保留区域（基于alpha通道，更准确地识别人像区域）
  const alpha = data[i + 3]
  const isPortraitArea = alpha > 128 // 降低阈值，包含更多人像区域，避免边框问题



== 手动精修流程 ==
1、watch currentSegment、mattingMaskImage
2、initCanvas => calculateCanvasSize => addPortraitRedOverlay
3、拖拽画笔逻辑 updateMattingImageFromCanvas，TODO：② drawRedMask 查出配置的参数为什么是黑色？③ hasRedOverlay逻辑是否需要优化(或者只有里面的历史继续需要优化)
4、撤销等逻辑
5、手动精修的图片同步

picture/matting on  matting 588.9ce4d8e3.js

TODO：resetMask() {  和 const reset = () => { 既不不一致
TODO（todo&mark）:
addPortraitRedOverlay 首次渲染时
drawRedMask 画笔拖拽时
updateMattingImageFromCanvas 画笔操作后更新画布（有异常值,红色区域判断） => initCanvas

TODO：
1、手动精修 抠图人像边缘处 存在异常色彩点 updateMattingImageFromCanvas
2、手动精修 原图背景存在红色时，抠图区域判断容易出问题（调整阈值 使画笔区域完整，规避图片中的红色区域）updateMattingImageFromCanvas
3、撤回功能
4、精修后图片同步
5、手动画笔需要规避相同点位，避免透明颜色叠加（暂时不重要）
6、代码优化
7、手动精修 重新生成的图边缘有毛刺，需要添加羽化效果（ai算法抠图的边缘存在半透明）（已完成）


================= demo ===================
已实现人像抠图功能，现在需要实现手动精修，支持保留和擦除功能，给一个demo
注意
1、画笔快速拖动时涂层需要连贯
2、蒙层颜色：rgba(246, 0, 0, 0.3)
3、人像、画笔 增加边缘标志，用来识别蒙层区域？因为蒙层颜色：rgba(246, 0, 0, 0.3)，叠加原图片本身存在红色通道时，总是识别不准确

================= 分析 ===================
1、drawRedMask 是否用arc去画，而不是moveTo、lineTo，这样边缘更加圆滑？示例：(待议，不一定能解决圆滑的事)
ctx.beginPath();
ctx.arc(x, y, brushRadius, 0, Math.PI * 2);
ctx.fill();

======================= 按功能分批进行组件提取、重构 ======================

======================= 羽化 =======================
1、addPortraitRedOverlay 中 Math.min(77, Math.floor(alpha * 0.3))
2、applyFeatheringToEdges
3、ctx.shadowBlur

======================= 提示词 620 ======================
背景
1、目前抠图功能正常，手动精修有问题
2、问题一：人像红色透明蒙层跟原图红色部分有重合，判断抠图区域容易有误
3、问题二：手画笔操作时，边缘需要羽化防止锯齿
4、问题三：生成目标抠图时，需要将抠图边缘做羽化

方案建议：
1、针对问题一：建议新建一个 redMaskCanvas 用作红色蒙层（z-index在 manualRefineCanvas 上面，原人像蒙层移到当前canvas上，支持画笔操作），跟 manualRefineCanvas 保持平级，在这两个canvas上增加一个父级 canvas-box ，将 manualRefineCanvas dom属性中的 style 移动到 canvas-box 上。核心方法：addPortraitRedOverlay
2、针对问题二：画笔操作现有逻辑不用变，注意要优化锯齿，擦除时只需要擦除 redMaskCanvas 上的相关有色点位，而不是使用：'rgba(0, 0, 0, 1)'。核心方法：drawRedMask
3、针对问题三：生成抠图时根据 redMaskCanvas 的有色点位，从currentImg.value.file里面取色还原，画到目标 originBoxRef 上（也就是batchStore.currentImg.mattingImage），注意生成后需要优化锯齿。核心方法：updateMattingImageFromCanvas
4、canvas-box 需要支持拖动，也就是原canvas里面的style，理论上挪移过去就支持了
5、完善画笔操作的历史记录，也就是注释中的代码：创建对应的蒙版图像（用于历史记录），历史记录的应该是 redMaskCanvas，撤销、恢复、重置时 从缓存中获取。核心方法：addMaskHistory、applyMaskHistory、resetMask

问题1
1、首次添加的人像蒙层，需要放到历史记录里面，重置的时候需要恢复到首次的样子
2、重置后日志发生了死循环，不知道是否因为死循环导致保留和擦除功能失效了
3、拖动功能选中时，鼠标应该是 原生样式的小手吧？
3、未使用画笔前边缘羽化是好的，使用画笔后边羽化存在问题边缘全是锯齿，同样updateMattingImageFromCanvas生成的目标图也都是锯齿
4、点击原图、换背景、改尺寸按钮时，才需要调用 updateMattingImageFromCanvas 更新currentImg.value.mattingImage吧？

问题2
1、点击 原图 按钮，也需要调用 updateMattingImageFromCanvas
2、原图上不需要红色蒙层，已被我去除; 首次添加的人像蒙层，需要放到历史记录里面，重置的时候需要恢复到首次的样子(指的是redMaskCtx)，重置时redMaskCanvas需要恢复到首次addPortraitRedOverlay(redMaskCtx, originalImg.width, originalImg.height)的样子，目前重置后，redMaskCanva完全透明
3、hasRedOverlayFN只需要判断是否有红色就可以了吧？因为redMaskCanvas只有单纯的蒙层红，只需要判断是否透明
4、保留画笔画完，能否使颜色保持纯色，如：'rgba(246, 0, 0, 0.3)'，现在是'rgba(246, 0, 0, 0.3)'的叠加色

问题3
1、保留 实现了纯色画笔，但中间会存在弧形白色，如图
2、撤销到底和重置的时候，redMaskCanvas 人像部分没了，注意人像是初始化的，重置或撤销到最后一个时希望是原始的人像抠图
3、原图、效果图按钮不断切换时，redMaskCanvas红色蒙层部分会不断扩大，是applyFeatheringToEdges导致的，需要修复
-- 我疑惑的是 addPortraitRedOverlay 中 Math.min(77, Math.floor(alpha * 0.3)) 的羽化效果还不错，是否可以参考

问题4
1、保留画笔回到上上个问题了：保留画笔画完，能否使颜色保持纯色，如：'rgba(246, 0, 0, 0.3)'，现在是'rgba(246, 0, 0, 0.3)'的叠加色，且中间不要存在弧形白色，未解决
-- 保留画笔结束操作时，将有色区域都改成rgba(246, 0, 0, 0.3)'
4、画笔圆角羽化程度不够
5、currentImg.mattingMaskImage 既然不用了就移除掉, 已完成
6、点击 按钮 重新选择 图片，选择后抠图前切换tab到 换背景, 已完成
7、tab是手动精修时，点击 立即导出 按钮，也需要调用 updateMattingImageFromCanvas,处理完之后才可以触发导出, 已完成
8、ChangeBackgroundByCategory.vue 中 Masonry图片 是否可以进行懒加载，因为图片有初始化占位，看看是否好处理，好处理就处理，不好处理就放弃, 已完成
9、设置画布的抗锯齿和图像平滑属性
targetCtx.imageSmoothingQuality = 'high'

问题5
1、关于重置
src/views/Matting/index.vue 和 src/pinia/matting.ts 部分方法定义重复了，如：applyMaskHistory、resetMask
解决：
将下面变量挪到 matting.ts 中
const manualRefineCanvas = ref<HTMLCanvasElement | null>(null) // 显示画布 - 显示原图和初始红色蒙层
const manualRefineCanvasContext = ref<CanvasRenderingContext2D | null>(null) // 缓存显示画布的上下文
const redMaskCanvas = ref<HTMLCanvasElement | null>(null) // 操作画布 - 用于红色蒙层操作
const redMaskCanvasContext = ref<CanvasRenderingContext2D | null>(null) // 缓存操作画布的上下文
将applyMaskHistory、resetMask合并到matting.ts中
确保功能运行正常，修改完先git add 一下，等等我在暂存区中看看是否ok


其他
1、关于重置，代码合并，处理历史记录，已完成
2、切换原图时，origin-box、canvas-box transform为什么会被重置？（自己思考下，ai有问题），已完成
3、组件重构

// 手动精修、换背景、改尺寸、抠图的代码可以提取到单独的ts中
需求：在不影响现有功能的前提下（注意保留原来注释掉的代码），将手动精修代码提取到单独的组件中
说明：
1、关于样式：.chessboard-bg是全局样式，不需要重新定义提取，.box可以复制一份过去，将index.scss中相关组件的样式也提取过去（并移除已提取的样式），注意不要添加新的样式
2、batchStore 通过组件的currentStore属性传递过去，不要重新定义变量，组件中的属性，优先使用currentStore里面的
3、如果是多个组件共用的方法、变量，可以提取到 matting.ts中
4、注意不要改坏原来功能，已提取的代码删除掉


TODO 不同tab，换背景和改尺寸 的图不一样，改尺寸 生成的图偏移了，换背景 中放大缩小后偏移更明显，问题也出在createMattingCropImage上